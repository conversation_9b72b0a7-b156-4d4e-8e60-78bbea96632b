services:
  php:
    build:
      context: .
      dockerfile: infrastructure/Dockerfile
    restart: unless-stopped
    entrypoint: php artisan octane:frankenphp
    ports:
      - '8000:8000'
    networks:
      - net
    logging:
      driver: json-file
      options:
        max-size: 10m
  task:
    build:
      context: .
      dockerfile: infrastructure/Dockerfile
    entrypoint: php artisan schedule:work
    restart: unless-stopped
    networks:
      - net
    logging:
      driver: json-file
      options:
        max-size: 10m
  reverb:
    build:
      context: .
      dockerfile: infrastructure/Dockerfile
    entrypoint: php artisan reverb:start
    ports:
      - '9000:8080'
    restart: unless-stopped
    networks:
      - net
    logging:
      driver: json-file
      options:
        max-size: 10m
  queue:
    build:
      context: .
      dockerfile: infrastructure/Dockerfile
    entrypoint: php artisan queue:work --tries=3
    restart: unless-stopped
    networks:
      - net
    logging:
      driver: json-file
      options:
        max-size: 10m

  tor-proxy:
    image: "dperson/torproxy"
    environment:
      - TOR_MaxCircuitDirtiness=1
      - TOR_NewCircuitPeriod=1
      - TOR_EnforceDistinctSubnets=1
    networks:
      - net
    restart: always
  watermark-api:
    build:
      context: erase-watermark
      dockerfile: Dockerfile
    networks:
      - net
    depends_on:
      - tor-proxy
    restart: always

  kibana:
    image: kibana:8.17.4
    ports:
      - '127.0.0.1:5601:5601'
      - '5601:5601'
    environment:
      ELASTICSEARCH_HOSTS: ${ES_HOSTS}
      ELASTIC_USERNAME: ${ES_USERNAME}
      ELASTIC_PASSWORD: ${ES_PASSWORD}
      SERVER_NAME: k.gunpost.ru
#      SERVER_HOST: "0.0.0.0"
#      XPACK_MONITORING_ENABLED: "true"
#      XPACK_SECURITY_ENABLED: "true"
#    volumes:
#      - kibana:/usr/share/kibana/data

networks:
  net:
    driver: bridge

volumes:
  kibana:
