services:
    php:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        restart: unless-stopped
        entrypoint: php artisan octane:frankenphp --workers=1 --max-requests=1
        ports:
            - '80:8000'
        networks:
            - net
        volumes:
            - '.:/app'
        logging:
            driver: json-file
            options:
                max-size: 10m
    task:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        entrypoint: php artisan schedule:work
        restart: unless-stopped
        networks:
            - net
        volumes:
            - '.:/app'
        logging:
            driver: json-file
            options:
                max-size: 10m
    queue:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        entrypoint: php artisan queue:work --tries=3
        restart: unless-stopped
        networks:
            - net
        volumes:
            - '.:/app'
        logging:
            driver: json-file
            options:
                max-size: 10m

    reverb:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        entrypoint: php artisan reverb:start --debug
        ports:
            - '9000:8080'
        restart: unless-stopped
        networks:
            - net
        volumes:
            - '.:/app'
        logging:
            driver: json-file
            options:
                max-size: 10m
    pgsql:
        image: 'postgres:17'
        ports:
            - '${FORWARD_DB_PORT:-5432}:5432'
        environment:
            PGPASSWORD: '${DB_PASSWORD:-secret}'
            POSTGRES_DB: '${DB_DATABASE}'
            POSTGRES_USER: '${DB_USERNAME}'
            POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
        volumes:
            - 'pgsql:/var/lib/postgresql/data'
        networks:
            - net
        healthcheck:
            test:
                - CMD
                - pg_isready
                - '-q'
                - '-d'
                - '${DB_DATABASE}'
                - '-U'
                - '${DB_USERNAME}'
            retries: 3
            timeout: 5s
    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'redis:/data'
        networks:
            - net
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    elasticsearch:
        image: 'docker.elastic.co/elasticsearch/elasticsearch:8.10.2'
        environment:
            - discovery.type=single-node
            - xpack.security.enabled=false
            - ES_JAVA_OPTS=-Xms1g -Xmx1g
        ports:
            - '9200:9200'
        volumes:
            - 'elasticsearch:/usr/share/elasticsearch/data'
        networks:
            - net
        restart: on-failure
    tor-proxy:
        image: "dperson/torproxy"
        environment:
            - TOR_MaxCircuitDirtiness=1
            - TOR_NewCircuitPeriod=1
            - TOR_EnforceDistinctSubnets=1
        networks:
            - net
        restart: always
    kibana:
        image: kibana:8.10.2
        ports:
            - '5601:5601'
        networks:
            - net
        depends_on:
            - elasticsearch
        environment:
            ELASTICSEARCH_HOSTS: ${ES_HOSTS}
            ELASTIC_USERNAME: ${ES_USERNAME}
            ELASTIC_PASSWORD: ${ES_PASSWORD}
    watermark-api:
        build:
            context: erase-watermark
            dockerfile: Dockerfile
        ports:
            - "3001:3001"
        networks:
            - net
        depends_on:
            - tor-proxy
        restart: always

networks:
    net:
        driver: bridge
volumes:
    pgsql:
    redis:
    elasticsearch:
