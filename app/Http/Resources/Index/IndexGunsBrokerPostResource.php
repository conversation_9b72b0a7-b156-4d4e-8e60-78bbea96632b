<?php

namespace App\Http\Resources\Index;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class IndexGunsBrokerPostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = Arr::except(parent::toArray($request), [
            '_id',
            'source',
            'updated_at',
            'created_at',
        ]);

        $match = 0;
        $total = count($data['photos']);

        foreach ($data['photos'] as $photo) {
            if (! str_contains($photo['url'], 'gunsbroker.ru')) {
                $match++;
            }
        }

        $percentage = $total
            ? round($match / $total * 100, 2)
            : 0;

        if ($percentage >= 0 && $percentage < 100) {
            $data['percentage'] = round($percentage, 2);
        }

        return $data;
    }
}
