<?php

namespace App\Http\Controllers;

use App\Events\PostView;
use App\Http\Requests\ReportRequest;
use App\Http\Resources\Index\IndexPostResource;
use App\Models\Category;
use App\Models\Index\IndexGetPhone;
use App\Models\Index\IndexPost;
use App\Models\Post;
use App\Models\RefModeration;
use App\Models\Report;
use App\Models\User;
use App\Notifications\PostReport;
use App\Services\PostService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class PostController extends Controller
{
    public function index(Request $request): AnonymousResourceCollection
    {
        if ($request->category === 'hunting' && $request->types) {
            $request->merge([
                'gun_types' => $request->types,
            ]);
        }

        $filtersData = Category::getFiltersFor($request->category);
        $filters = $request->only(array_keys($filtersData));

        $postService = new PostService;

        $postsData = $postService->getPostsWithVip($request, $filters);

        $posts = $postsData['posts'];
        $is_empty = $postsData['is_empty'];
        $vip = $postsData['vip'];

        $category = $postService->getCategoryData($request->category);
        $attrs = $postService->getFilters($category, true);
        $seo = $postService->getSeoData($request->path);
        $city = $postService->getCityData($request->city);
        $type = $postService->getGunTypeData($request->type);

        return IndexPostResource::collection($posts)->additional(array_filter([
            'is_empty' => $is_empty,
            'filters' => array_filter($attrs),
            'city' => $city,
            'category' => $category,
            'type' => $type,
            'seo' => $seo,
            'vipAds' => IndexPostResource::collection($vip),
        ]));
    }

    public function show(string $postSlug, Request $request)
    {
        $user = auth('sanctum')->user();
        $is_preview = false;

        if ($request->has('preview')) {
            $is_preview = true;
            $post = IndexPost::withoutGlobalScopes()
                ->when($user?->role !== User::ROLE_ADMIN, function ($query) use ($user) {
                    return $query->where('user_id', $user?->id);
                })
                ->where('slug', $postSlug);
        } else {
            $post = IndexPost::withoutGlobalScopes()
                ->whereNot('moderation_id', RefModeration::IS_BANNED)
                ->whereNotNull('published_at')
                ->where('slug', $postSlug);
        }

        $post = $post->firstOrFail();

        PostView::dispatch($post->slug, $request->fingerprint(), Auth::id());

        $limit = $post->archived_reason ? 4 : 9;

        $other = IndexPost::where('category', $post->category)
            ->where('archived_reason', false)
            ->whereNotNull('published_at')
            ->city($post->city['slug'] ?? null)
            ->where('slug', '!=', $post->slug)
            ->orderBy('created_at')
            ->limit($limit)
            ->get();

        $favoritable = new Post;
        $favoritable->id = $post->id;

        $vip = IndexPost::where('archived_reason', false)
            ->whereNotNull('published_at')
            ->whereNotNull('promotion.vip')
            ->whereNot('slug', $post->slug)
            ->category($post->category)
            ->orderByDesc('promotion_start_date')
            ->limit(3)
            ->get();

        return response()->json(array_filter([
            'post' => new IndexPostResource($post),
            'other' => IndexPostResource::collection($other),
            'vipAds' => IndexPostResource::collection($vip),
            'is_preview' => $is_preview,
            'is_owner' => $post->user_id === $user?->id,
            'is_favorite' => $user?->hasFavorited($favoritable),
        ]));
    }

    public function getPhone(Post $post, Request $request): JsonResponse
    {
        $user = auth('sanctum')->user();

        IndexGetPhone::withoutRefresh()->insert([
            'post_id' => $post->id,
            'fingerprint' => $request->fingerprint(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $user?->id,
            'created_at' => now(),
        ]);

        return response()->json([
            'ok' => true,
            'seller' => [
                'name' => $post->user?->name,
                'avatar' => [
                    'alt' => $post->user?->name,
                    'src' => $post->user->avatar,
                ],
            ],
            'phone' => $post->user->phone,
        ]);
    }

    public function report(IndexPost $post, ReportRequest $request)
    {
        $report = Report::firstOrCreate([
            'user_id' => auth('sanctum')?->id(),
            'post_id' => $post->id,
            'fingerprint' => $request->fingerprint(),
            'message' => $request->message,
            'email' => $request->email,
        ]);

        $serviceUser = User::where('email', User::SUPPORT_USER_EMAIL)->firstOrFail();
        Notification::send($serviceUser, new PostReport($post, $report));

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function toggleFavorite(Post $post)
    {
        $user = Auth::user();
        $user->toggleFavorite($post);

        return response()->json([
            'ok' => true,
        ], 201);
    }
}
