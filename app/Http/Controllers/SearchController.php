<?php

namespace App\Http\Controllers;

use App\Http\Resources\Index\IndexPostSearchResource;
use App\Models\Index\IndexPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use JeroenG\Explorer\Domain\Syntax\Matching;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        try {
            $posts = IndexPost::search($request->q)
                ->query(function ($query) {
                    $query->where('published_at', '!=', null);
                });

            if ($request->city_slug) {
                // $posts = $posts->filter(new Matching('city_slug', $request->city_slug));
            }

            if ($request->category) {
                $posts = $posts->filter(new Matching('category', $request->category));
            }

        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return response()->json([
                'ok' => false,
            ], 400);
        }

        $posts = $posts
            ->where('archived_reason', false)
            ->orderByDesc('promotion_start_date')
            ->take(20)
            ->get();

        return IndexPostSearchResource::collection($posts);
    }
}
