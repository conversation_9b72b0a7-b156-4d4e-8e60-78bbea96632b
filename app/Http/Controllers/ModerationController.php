<?php

namespace App\Http\Controllers;

use App\Events\PostChangeEvent;
use App\Http\Resources\PostCreateResource;
use App\Http\Resources\ReportResource;
use App\Models\Post;
use App\Models\RefModeration;
use App\Models\Report;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class ModerationController extends Controller
{
    public function reports(Request $request)
    {
        $reports = Report::with(['user', 'post', 'post.category', 'post.city'])->paginate();

        return ReportResource::collection($reports);
    }

    public function posts(Request $request): AnonymousResourceCollection
    {
        $posts = Post::withoutGlobalScopes()
            ->where('moderation_id', $request->input('moderation', RefModeration::IS_NOT_APPROVED))
            ->whereNotNull('published_at')
            ->with(['category', 'city'])
            ->orderBy('published_at')
            ->paginate();

        return PostCreateResource::collection($posts);
    }

    public function moderation(string $slug, Request $request): JsonResponse
    {
        Log::debug('Moderation', ['slug' => $slug, 'moderation' => $request->moderation]);

        $post = Post::withoutGlobalScopes()
            ->where('slug', $slug)
            ->firstOrFail();

        $post->moderation_id = $request->moderation;
        //        $post->published_at = now();
        $post->save();

        PostChangeEvent::dispatch($post);

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function reIndex(string $slug): JsonResponse
    {
        $post = Post::withoutGlobalScopes()
            ->where('slug', $slug)
            ->firstOrFail();

        PostChangeEvent::dispatch($post);

        return response()->json([
            'ok' => true,
        ], 201);
    }
}
