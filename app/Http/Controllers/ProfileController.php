<?php

namespace App\Http\Controllers;

use App\Http\Requests\SetPasswordRequest;
use App\Http\Requests\UpdatePasswordRequest;
use App\Http\Resources\Index\IndexPostResource;
use App\Http\Resources\UserPublicResource;
use App\Models\Index\IndexPost;
use App\Models\RefCity;
use App\Models\User;
use App\Models\UserConfirmation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Overtrue\LaravelFavorite\Favorite;

class ProfileController extends Controller
{
    public function public(User $user, Request $request)
    {
        if ($user->role === 'admin') {
            // abort(404);
        }

        $posts = IndexPost::where('user_id', $user->id)
            ->where('archived_reason', false)
            ->whereNotNull('published_at')
            ->orderByDesc('views')
            ->paginate();

        return IndexPostResource::collection($posts)->additional(
            [
                'user' => new UserPublicResource($user),
            ]
        );
    }

    public function favorites(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        $ids = Favorite::where('user_id', $user->id)->pluck('favoriteable_id');
        $favorites = IndexPost::whereIn('id', $ids)->paginate();

        return IndexPostResource::collection($favorites);
    }

    public function setCity(Request $request): JsonResponse
    {
        $city = RefCity::where('slug', $request->city)->firstOrFail();

        $request->user()->update([
            'ref_city_id' => $city->id,
        ]);

        return response()->json(['ok' => true], 201);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|min:3|max:255',
            'bio' => 'nullable|min:3|max:2000',
            'theme' => 'required|in:light,dark,system',
        ]);

        $user = Auth::user();

        $user->update([
            'name' => $request->name,
            'about' => $request->bio,
            'theme' => $request->theme,
        ]);

        if ($request->hasFile('avatar')) {
            try {
                $user->addMedia(
                    file: $request->file('avatar'),
                    collectionName: 'avatar',
                    name: "{$user->id}-avatar"
                );
            } catch (\Throwable $exception) {
                Log::error($exception);
            }
        }

        return response()->json(['ok' => true], 201);
    }

    public function setPassword(SetPasswordRequest $request): JsonResponse
    {
        $user = Auth::user();

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return response()->json(['ok' => true], 201);
    }

    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {

        $user = Auth::user();

        $user->update([
            'password' => Hash::make($request->new_password),
        ]);

        return response()->json(['ok' => true], 201);
    }

    public function acceptBanner(Request $request): JsonResponse
    {
        $banner = in_array($request->type, UserConfirmation::BANNERS);

        if (! $banner) {
            return response()->json(['ok' => true], 201);
        }

        UserConfirmation::firstOrCreate([
            'user_id' => Auth::id(),
            'type' => $request->type,
        ]);

        return response()->json(['ok' => true], 201);
    }
}
