<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Events\UserRegisteredEvent;
use Bavix\Wallet\Interfaces\Confirmable;
use Bavix\Wallet\Interfaces\Customer;
use Bavix\Wallet\Traits\CanConfirm;
use Bavix\Wallet\Traits\CanPay;
use Bavix\Wallet\Traits\HasWallet;
use Cmgmyr\Messenger\Traits\Messagable;
use Elegantly\Media\Concerns\HasMedia;
use Elegantly\Media\Definitions\MediaConversionImage;
use Elegantly\Media\MediaCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Overtrue\LaravelFavorite\Traits\Favoriter;
use Propaganistas\LaravelPhone\Casts\E164PhoneNumberCast;

class User extends Authenticatable implements Confirmable, Customer
{
    use CanConfirm;
    use CanPay;
    use Favoriter;
    use HasApiTokens;
    use HasFactory;
    use HasMedia;
    use HasWallet;
    use Messagable;
    use Notifiable;

    const ROLE_USER = 'user';

    const ROLE_ADMIN = 'admin';

    const SUPPORT_USER_EMAIL = '<EMAIL>';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'ref_city_id',
        'support_chat_id',
        'role',
        'is_shop',
        'telegram_chat_id',
        'theme',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public $casts = [
        // TODO работает не согласованно с регистрацией
        //        'phone' => E164PhoneNumberCast::class.':RU',
    ];

    protected $dispatchesEvents = [
        'created' => UserRegisteredEvent::class,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function registerMediaCollections(): array
    {
        return [
            new MediaCollection(
                name: 'avatar',
                acceptedMimeTypes: [ // (optional) Specify accepted file types
                    'image/jpeg',
                    'image/png',
                    'image/webp',
                ], // If true, only the latest file will be kept
                single: true, // (optional) Specify where the file will be stored
                conversions: [
                    new MediaConversionImage(
                        name: '50',
                        queued: true,
                        width: 50, // Conversion will not be generated at upload time
                    ),
                ]
            ),
        ];
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(RefCity::class, 'ref_city_id');
    }

    public function confirmations(): HasMany
    {
        return $this->hasMany(UserConfirmation::class);
    }

    public function telegram(): BelongsTo
    {
        return $this->belongsTo(UserTelegram::class, 'id', 'user_id');
    }
}
