<?php

namespace App\Models\Index;

use App\Models\Scopes\ApprovedScope;
use JeroenG\Explorer\Application\Explored;
use Laravel\Scout\Searchable;
use PDPhilip\Elasticsearch\Eloquent\Model;
use PDPhilip\Elasticsearch\Query\Builder;

class IndexPost extends Model implements Explored
{
    use Searchable;

    protected $connection = 'elasticsearch';

    protected $index = 'gunpost_posts';

    protected $guarded = [];

    protected static function booted(): void
    {
        static::addGlobalScope(new ApprovedScope);
    }

    public function mappableAs(): array
    {
        return [
            'slug' => 'keyword',
            'city_slug' => 'keyword',
            'category' => 'keyword',
            'description' => 'text',
            'title' => 'text',
            'published_at' => 'date',
            'promotion_start_date' => 'date',
        ];
    }

    public function scopeFilter($query, array $filters): void
    {
        foreach ($filters as $type => $slug) {
            $query->whereNestedObject('attributes', function (Builder $query) use ($type, $slug) {
                $query->where('type', $type);
                $query->where('slug', $slug);
            });
        }
    }

    public function scopeCity($query, ?string $slug): void
    {
        if (! empty($slug)) {
            $query->where('city_slug', $slug);
        }
    }

    public function scopeCategory($query, ?string $slug): void
    {
        if (! empty($slug)) {
            $query->where('category', $slug);
        }
    }

    public function scopeType($query, ?string $slug, ?string $category): void
    {
        if (! empty($slug)) {
            $query->where('type', $slug);
            $query->where('category', $category);
        }
    }

    public function scopePrice($query, ?int $min_price, ?int $max_price): void
    {
        if ($min_price !== null) {
            $query->where('price', '>=', $min_price);
        }

        if ($max_price !== null) {
            $query->where('price', '<=', $max_price);
        }
    }
}
