<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\Index\IndexAddress;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\RefCaliber;
use App\Models\RefCity;
use App\Models\RefCondition;
use App\Models\RefGunOrientation;
use App\Models\RefGunReloading;
use App\Models\RefGunType;
use App\Models\RefModeration;
use Drnxloc\LaravelHtmlDom\HtmlDomParser;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Propaganistas\LaravelPhone\PhoneNumber;

class ParseGunsBroker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parse:gunsbroker';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $cookieJar;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //        IndexGunsBrokerPost::truncate();

        $categories = Category::whereNotIn('slug', ['hunting'])->get();
        foreach ($categories as $category) {
            $this->parseCategory($category->slug);
        }

        //        $this->parseWithAttributes();

    }

    private function parseWithAttributes()
    {
        $filters = [
            'cal' => [
                '101' => '12 калибр',
                '102' => '16 калибр',
                '103' => '20 калибр',
                '104' => '28 калибр',
                '105' => '32 калибр',
                //                '106' => '410 калибр',
                //                '201' => '.22',
                //                '202' => '223 Rem',
                //                '203' => '308 Win',
                //                '204' => '243 Win',
                //                '205' => '338',
                //                                '206' => '5,45×39 мм',
                //                '207' => '7,62',
                //                '208' => '9x'
            ],
            'reloading-type' => [
                '21' => 'Помповое',
                '22' => 'Полуавтоматическое',
                '23' => 'Болтовое',
                '24' => 'Скоба Генри',
                '25' => 'Переломное',
            ],
            'stvol-type' => [
                '1' => 'Одноствольное',
                '2' => 'Горизонтальное',
                '3' => 'Вертикальное',
            ],
        ];

        foreach ($filters as $filter_name => $value) {
            foreach ($value as $filter_value => $label) {
                if ($filter_name === 'cal') {
                    try {
                        $caliber = RefCaliber::where('name', $label)?->first();

                        if ($caliber?->name) {
                            $this->info($caliber?->name);
                            $this->parseUrl($filter_name, $filter_value, $caliber);
                        } else {
                            $this->warn($label);
                        }
                    } catch (\Throwable $throwable) {
                        $this->error($throwable->getMessage());
                    }
                }

                if ($filter_name === 'reloading-type' && $label != 'Любая система перезарядки') {
                    try {
                        $reloading_type = RefGunReloading::firstOrCreate([
                            'name' => $label,
                            'slug' => Str::slug($label),
                            'moderation_id' => RefModeration::IS_APPROVED,
                        ]);

                        if ($reloading_type?->name) {
                            $this->info($reloading_type?->name);
                            $this->parseUrl($filter_name, $filter_value, $reloading_type, 9);
                        } else {
                            $this->warn($label);
                        }
                    } catch (\Throwable $throwable) {
                        $this->error($throwable->getMessage());
                    }
                }

                if ($filter_name === 'stvol-type' && $label != 'Любое положение стволов') {
                    try {
                        $orientation = RefGunOrientation::firstOrCreate([
                            'name' => $label,
                            'slug' => Str::slug($label),
                            'moderation_id' => RefModeration::IS_APPROVED,
                        ]);

                        if ($orientation?->name) {
                            $this->info($orientation?->name);
                            $this->parseUrl($filter_name, $filter_value, $orientation);
                        } else {
                            $this->warn($label);
                        }
                    } catch (\Throwable $throwable) {
                        $this->error($throwable->getMessage());
                    }
                }
            }
        }
    }

    public function parseCategory($category, $page = 1): void
    {
        $this->line("page: $page; category: $category;");
        $url = "https://gunsbroker.ru/$category/page_$page";

        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
        ])->get($url);

        $dom = HtmlDomParser::str_get_html($response->body());
        $elements = $dom->find('.main__item--desc hgroup a');

        foreach ($elements as $element) {
            $indexModel = null;
            $href = null;
            try {
                $href = "https://gunsbroker.ru$element->href";
                $indexModel = IndexGunsBrokerPost::where('source', $href)->first();

                if (! $indexModel) {
                    $indexModel = new IndexGunsBrokerPost;
                }

                $postData = $this->parsePost($href);

                $postData['source'] = $href;

                if ($postData['address_id']) {
                    $address = IndexAddress::find($postData['address_id']);
                    if ($address) {
                        $postData['address_string'] = $address->result;
                        $this->line($address->result);
                        $region_code = $address->region_iso_code;
                        $city = RefCity::where('region_iso_code', $region_code)->first();

                        if ($city?->id) {
                            $postData['ref_city_id'] = $city->id;
                        }
                    }
                }

                $indexModel->fill($postData);
                $indexModel->withoutRefresh()->save();

            } catch (\Throwable $throwable) {
                $this->error($throwable->getMessage());
            }
        }

        if (count($elements) >= 10 && $page <= 3) {
            $this->parseCategory($category, $page + 1);
        }
    }

    /**
     * @throws ConnectionException
     */
    public function parseUrl($filter_name, $filter_value, $attributeModel, $page = 1): void
    {
        $this->line("page: $page; attribute: $attributeModel->name; name: $filter_name; value: $filter_value;");
        $url = "https://gunsbroker.ru/search/none/&{$filter_name}={$filter_value}&page=$page";

        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
        ])->get($url);

        $dom = HtmlDomParser::str_get_html($response->body());
        $elements = $dom->find('.main__item--desc hgroup a');

        foreach ($elements as $element) {
            $indexModel = null;
            $href = null;
            try {
                $href = "https://gunsbroker.ru$element->href";
                $indexModel = IndexGunsBrokerPost::where('source', $href)->first();

                if (! $indexModel) {
                    $indexModel = new IndexGunsBrokerPost;
                }

                $postData = $this->parsePost($href);

                $postData['source'] = $href;
                $postData['attributes'][$filter_name] = $attributeModel->slug;
                $postData['attributes'] = $this->setAttributes($postData['attributes']);

                if ($postData['address_id']) {
                    $address = IndexAddress::find($postData['address_id']);
                    if ($address) {
                        $postData['address_string'] = $address->result;
                        $this->line($address->result);
                        $region_code = $address->region_iso_code;
                        $city = RefCity::where('region_iso_code', $region_code)->first();

                        if ($city?->id) {
                            $postData['ref_city_id'] = $city->id;
                            //                            $postData['attributes'][] = [
                            //                                'attributeable_type' => RefCity::class,
                            //                                'attributeable_id' => $city->id,
                            //                            ];
                        }
                    }
                }

                $indexModel->fill($postData);
                $indexModel->withoutRefresh()->save();

            } catch (\Throwable $throwable) {
                $this->error($throwable->getMessage());
            }
        }

        if (count($elements) >= 10 && $page <= 3) {
            $this->parseUrl($filter_name, $filter_value, $attributeModel, $page + 1);
        }
    }

    private function parsePost($url): array
    {
        $this->line("Parse: $url");
        $this->cookieJar = new CookieJar;

        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
            'cookies' => $this->cookieJar,
        ])->get($url);

        $dom = HtmlDomParser::str_get_html($response->body());

        // Извлекаем `data-id` и `data-check` для номера телефона
        $data_id = $dom->find('.page-product__price-btn', 0)?->getAttribute('data-id');
        $data_check = $dom->find('.page-product__price-btn', 0)?->getAttribute('data-check');

        $user_name = $dom->find('.page-product__desc--seller strong a', 0)?->plaintext;
        $user_avatar = $dom->find('.page-product__desc--seller a img', 0)?->src;
        $user_id = $dom->find('.page-product__desc--seller strong a', 0)?->href;
        $user_id = (int) preg_replace('/\D/', '', $user_id);
        $user_phone = $this->getPhone($data_id, $data_check);

        $title = $dom->find('main.main__content h1', 0)?->plaintext;
        $title = $this->removeExtraSpaces($title);

        $location = $dom->find('.page-product__header--location', 0)?->plaintext;
        $location = $this->removeExtraSpaces($location);

        $price = $dom->find('.price_h2', 0)?->plaintext;
        $price = (int) preg_replace('/\D/', '', $price);

        $description = $dom->find('.page-product__content--desc p', 0)?->plaintext;

        $is_rebate = (bool) $dom->find('.page-product__price.rebate', 0);
        $is_trade = (bool) $dom->find('.page-product__price.trade', 0);

        $category = $dom->find('.bread-crumbs__item', 1)?->href;
        $category = preg_replace('/[^A-Za-z0-9.!?]/', '', $category);

        $parametrs = $dom->find('.page-product__desc--table div');

        $structuredData = [];
        $lastKey = null;

        foreach ($parametrs as $parametr) {
            $text = trim($parametr?->plaintext);

            // Если текст оканчивается на ":", считаем его ключом
            if (Str::endsWith($text, ':')) {
                $lastKey = rtrim($text, ':');
            } elseif ($lastKey) {
                // Если есть сохранённый ключ, то это значение для него
                $structuredData[$lastKey] = $text;
                $lastKey = null; // Сбрасываем ключ
            }
        }

        //        $images = $dom->find('#product-slider.owl-carousel img');
        //        $photos = [];
        //        foreach ($images as $img) {
        //            $photos[] = $img?->src;
        //        }

        $address_id = null;
        //        try {
        //            $address_id = $this->getAddress($location);
        //        } catch (\Throwable $throwable) {
        //            $this->error($throwable->getMessage());
        //        }

        if (in_array($user_avatar, ['/img/u/nopic.jpg', 'https://gunsbroker.ru/img/u/nopic.jpg'])) {
            $user_avatar = null;
        }

        return [
            'category' => $category,
            'title' => $title,
            'address' => $location,
            'address_id' => $address_id,
            'price' => $price,
            'is_rebate' => $is_rebate,
            'is_trade' => $is_trade,
            'attributes' => $structuredData,
            //            'photos' => $photos,
            'description' => $description,
            'user_id' => $user_id,
            'user_name' => $user_name,
            'user_avatar' => $user_avatar,
            'user_phone' => $user_phone,
        ];
    }

    private function getAddress($string)
    {
        $string = $this->removeExtraSpaces(trim($string));
        $address = IndexAddress::where('source', $string)->first();

        if ($address?->_id) {
            return $address->_id;
        }

        $response = Http::withOptions([
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Token '.config('services.dadata.api_key'),
                'X-Secret' => config('services.dadata.api_secret'),
            ],
        ])->post('https://cleaner.dadata.ru/api/v1/clean/address', [
            $string,
        ]);

        $json = $response->json();

        $address_id = null;
        foreach ($json as $index => $data) {
            $address = new IndexAddress;
            $address->fill($data);
            $address->withoutRefresh()->save();

            if ($index === 0) {
                $address_id = $address->_id;
            }
        }

        return $address_id ?? $address->_id;
    }

    /**
     * @throws ConnectionException
     */
    private function getPhone($data_id, $data_check): string
    {
        $url = "https://gunsbroker.ru/phpjs/get_number.php?n={$data_id}&c={$data_check}";

        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
            'cookies' => $this->cookieJar,
        ])->get($url);

        return (string) new PhoneNumber($response->body(), 'RU');
    }

    private function setAttributes(array $parameters): array
    {
        $data = [];

        foreach ($parameters as $parameter => $value) {
            if (Str::lower($parameter) === 'состояние') {
                $condition = RefCondition::whereRaw('LOWER(name) = ?', [Str::lower($value)])->first();
                if (! $condition) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefCondition::class,
                    'attributeable_id' => $condition->id,
                ];
            }

            if (Str::lower($parameter) === 'тип') {
                $type = RefGunType::whereRaw('LOWER(name) = ?', [Str::lower($value)])->first();
                if (! $type) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefGunType::class,
                    'attributeable_id' => $type->id,
                ];
            }

            if (Str::lower($parameter) === 'год выпуска') {
                $year = (int) $value;
            }

            if (Str::lower($parameter) === 'cal') {
                $caliber = RefCaliber::where('slug', $value)->first();
                if (! $caliber) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefCaliber::class,
                    'attributeable_id' => $caliber->id,
                ];

                $data[] = [
                    'attributeable_type' => RefGunType::class,
                    'attributeable_id' => $caliber->ref_gun_type_id,
                ];
            }

            if (Str::lower($parameter) === 'reloading-type') {
                $reloading = RefGunReloading::where('slug', $value)->first();
                if (! $reloading) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefGunReloading::class,
                    'attributeable_id' => $reloading->id,
                ];
            }
        }

        return $data;
    }

    private function removeExtraSpaces($string): array|string|null
    {
        return preg_replace('/\s+/', ' ', trim($string));
    }
}
