<?php

namespace App\Listeners;

use App\Events\PostChangeEvent;
use App\Http\Resources\MediaPostImagesResource;
use App\Models\Category;
use App\Models\Index\IndexPost;
use App\Services\PromotionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;

class PostChangeHandle implements ShouldQueue
{
    public function __construct(
        private PromotionService $promotionService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(PostChangeEvent $event): void
    {
        $post = $event->post;

        $post->load(['user', 'attributes', 'archived', 'promotions', 'promotions.promotionType']);

        $postData = [
            '_id' => $post->id,
            'id' => $post->id,
            'title' => $post->title,
            'slug' => $post->slug,
            'description' => $post->description,
            'price' => $post->price,
            'user_id' => $post->user_id,
            'seller_name' => $post->user?->name,
            'seller_avatar' => $post->user?->getFirstMedia('avatar')?->getUrl(conversion: '50', fallback: true) ?? '/images/avatar.jpg',
            'seller_id' => $post->user?->id,
            'seller_is_shop' => $post->user?->is_shop,
            'year' => $post->year,
            'registration_type' => $post->registration_type,
            'category' => $post->category->slug,
            'category_name' => $post->category->name,
            'source' => $post->source,
            'views' => $post->views,
            'favorites' => $post->favorites,
            'address' => $post->address,
            'address_geo' => $post->address_geo ? ['lat' => $post->address_geo[0], 'lon' => $post->address_geo[1]] : null,
            'registration_address' => $post->registration_address,
            'registration_geo' => $post->registration_geo ? ['lat' => $post->registration_geo[0], 'lon' => $post->registration_geo[1]] : null,
            'is_rebate' => $post->is_rebate,
            'is_trade' => $post->is_trade,
            'can_ship' => $post->can_ship,
            'archived_reason' => (bool) $post->archived?->name,
            'published_at' => $post->published_at,
            'moderation_id' => $post->moderation_id,
        ];

        if ($post->description) {
            $description = str_replace(["\r\n", "\n", "\r"], ' ', $post->description);
            $postData['description_short'] = Str::limit($description, 150);
        }

        if ($post?->city?->slug) {
            $postData['city_slug'] = $post->city->slug;
            $postData['city_name'] = $post->city->name;
            $postData['city_name_pred'] = $post->city->name_pred;
        }

        foreach ($post->attributes as $attribute) {
            try {
                $attr = $attribute->attributeable;
                $modelTable = $attr->getTable();
                $attribute_name = preg_replace('/^ref_/', '', $modelTable);

                $filter = Category::ALL_FILTERS[$attribute_name];
                if ($attribute_name === 'anies') {
                    $postData['attributes'][] = [
                        'type' => $attribute_name,
                        'label' => $attr->name,
                        'name' => $attr->value,
                        'slug' => $attr?->slug ?? Str::slug($attr->name),
                    ];
                } else {
                    $postData['attributes'][] = [
                        'type' => $attribute_name,
                        'label' => $filter['label'] ?? $attribute_name,
                        'name' => $attr->name,
                        'slug' => $attr?->slug ?? Str::slug($attr->name),
                    ];
                }
            } catch (\Throwable $th) {
                continue;
            }
        }

        $thumbnails = [];
        foreach ($post->getMedia('images') as $media) {
            $thumbnails[] = $media->getUrl(
                conversion: '350',
                fallback: false
            );
        }

        $postData['thumbnails'] = $thumbnails;
        $postData['images'] = MediaPostImagesResource::collection($post->getMedia('images'));

        // Получаем активные продвижения через сервис
        $promotionData = $this->promotionService->getPromotionData($post);

        $postData['promotion'] = $promotionData['promotion'] ?? null;
        $postData['promotion_start_date'] = $promotionData['promotion_start_date'] ?? null;

        IndexPost::withoutRefresh()->updateOrCreate(
            ['slug' => $post->slug],
            $postData
        );
    }
}
