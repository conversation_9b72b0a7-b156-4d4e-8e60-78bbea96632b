<?php

namespace App\Services;

use App\Models\Index\IndexPost;
use App\Models\Post;
use App\Models\Promotion;
use App\Models\PromotionType;
use App\Services\Promotion\Exceptions\PromotionAlreadyActiveException;
use App\Services\Promotion\Exceptions\PromotionExpiredException;
use App\Services\Promotion\Handlers\ColorPromotionHandler;
use App\Services\Promotion\Handlers\UpPromotionHandler;
use App\Services\Promotion\Handlers\VipPromotionHandler;
use Bavix\Wallet\Exceptions\BalanceIsEmpty;
use Bavix\Wallet\Exceptions\InsufficientFunds;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для работы с продвижением постов
 */
class PromotionService
{
    private array $handlers = [];

    public function __construct(
        private UpPromotionHandler $upHandler,
        private ColorPromotionHandler $colorHandler,
        private VipPromotionHandler $vipHandler
    ) {
        $this->handlers = [
            'up' => $this->upHandler,
            'color' => $this->colorHandler,
            'vip' => $this->vipHandler,
        ];
    }

    /**
     * Создает новое продвижение для поста
     *
     * @param  Post  $post  Пост, для которого создается продвижение
     * @param  PromotionType  $promotionType  Тип продвижения
     * @return Promotion Созданное продвижение
     *
     * @throws PromotionAlreadyActiveException Если продвижение уже активно
     * @throws \Exception Если обработчик для типа продвижения не найден
     */
    public function createPromotion(Post $post, PromotionType $promotionType): Promotion
    {
        $handler = $this->handlers[$promotionType->type] ?? null;
        if (! $handler) {
            throw new \Exception("Handler for promotion type {$promotionType->type} not found");
        }

        // Создание продвижения через хэндлер
        return $handler->create($post, $promotionType);
    }

    /**
     * Обрабатывает покупку продвижения
     *
     * @param  PromotionType  $promotionType  Тип продвижения
     * @return bool Результат обработки
     *
     * @throws BalanceIsEmpty Если баланс пользователя пуст
     * @throws InsufficientFunds Если недостаточно средств
     * @throws PromotionExpiredException Если срок действия продвижения истек
     */
    public function payForPromotion(Promotion $promotion): bool
    {
        return DB::transaction(function () use ($promotion) {

            /**
             * Используем метод pay вместо withdraw
             * Вернет Exception если недостаточно средств
             */
            $promotion->post->user->pay($promotion);

            $promotion->update([
                'purchased_at' => Carbon::now(),
            ]);

            $this->handlers[$promotion->promotionType->type]->process($promotion);
            $this->updatePostIndex($promotion->post);

            return true;
        });
    }

    public function getPromotionData(Post $post): array
    {
        $activePromotions = $this->getActivePromotions($post);
        $promotionData = $this->formatPromotionsForIndex($activePromotions);

        return [
            'promotion' => $promotionData['promotion'],
            'promotion_start_date' => $promotionData['promotion_start_date'],
        ];
    }

    /**
     * Обновляет индекс поста с учетом всех активных продвижений
     *
     * @param  Post  $post  Пост для обновления индекса
     */
    public function updatePostIndex(Post $post): void
    {
        $promotionData = $this->getPromotionData($post);

        $indexModel = IndexPost::where('slug', $post->slug)->firstOrFail();
        $indexModel->promotion = $promotionData['promotion'];
        $indexModel->promotion_start_date = $promotionData['promotion_start_date'];
        $indexModel->withoutRefresh()->save();
    }

    /**
     * Проверяет и обрабатывает истекшие продвижения
     *
     * Метод находит все продвижения с истекшим сроком действия и пытается их удалить.
     */
    public function processExpiredPromotions(): void
    {
        Promotion::where('expires_at', '<', Carbon::now())
            ->delete();
    }

    /**
     * Получает активные продвижения для поста
     *
     * @param  Post  $post  Пост, для которого запрашиваются продвижения
     * @return \Illuminate\Database\Eloquent\Collection Список активных продвижений
     */
    public function getActivePromotions(Post $post)
    {
        return $post->promotions()
            ->with('promotionType')
            ->where('expires_at', '>', Carbon::now())
            ->where('created_at', '<=', Carbon::now())
            ->whereNotNull('processed_at')
            ->whereNotNull('purchased_at')
            ->get();
    }

    /**
     * Форматирует данные о продвижениях для индекса
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $activePromotions  Список активных продвижений
     * @return array Отформатированные данные о продвижениях
     */
    public function formatPromotionsForIndex($activePromotions): array
    {
        $promotionData = [
            'promotion' => [],
            'promotion_start_date' => null,
        ];

        if (! $activePromotions) {
            return $promotionData;
        }

        foreach ($activePromotions as $promotion) {
            $promotionData['promotion'][$promotion->promotionType->type] = $promotion->processed_at->toISOString();

            if ($promotion->promotionType->type === 'up') {
                $promotionData['promotion_start_date'] = $promotion->processed_at->toISOString();
            }
        }

        return $promotionData;
    }
}
