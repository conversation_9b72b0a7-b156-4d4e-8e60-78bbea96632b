<?php

namespace App\Services\Parse;

use App\Http\Resources\Index\IndexGunsBrokerPostResource;
use App\Models\Category;
use App\Models\Index\IndexAddress;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\Post;
use App\Models\PostAttribute;
use App\Models\RefCaliber;
use App\Models\RefCity;
use App\Models\RefCondition;
use App\Models\RefGunReloading;
use App\Models\RefGunType;
use App\Models\RefModeration;
use Drnxloc\LaravelHtmlDom\HtmlDomParser;
use Elegantly\Media\Models\Media;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Propaganistas\LaravelPhone\PhoneNumber;

class GunsbrokerService
{
    public array $filters = [
        'cal' => [
            '101' => '12 калибр',
            '102' => '16 калибр',
            '103' => '20 калибр',
            '104' => '28 калибр',
            '105' => '32 калибр',
            //                '106' => '410 калибр',
            //                '201' => '.22',
            //                '202' => '223 Rem',
            //                '203' => '308 Win',
            //                '204' => '243 Win',
            //                '205' => '338',
            //                                '206' => '5,45×39 мм',
            //                '207' => '7,62',
            //                '208' => '9x'
        ],
        'reloading-type' => [
            '21' => 'Помповое',
            '22' => 'Полуавтоматическое',
            '23' => 'Болтовое',
            '24' => 'Скоба Генри',
            '25' => 'Переломное',
        ],
        'stvol-type' => [
            '1' => 'Одноствольное',
            '2' => 'Горизонтальное',
            '3' => 'Вертикальное',
        ],
    ];

    public CookieJar $cookieJar;

    public function __construct()
    {
        $this->cookieJar = new CookieJar;
    }

    /**
     * @throws \Throwable
     */
    public function savePost(IndexGunsBrokerPost $product, int $userId): Post
    {
        $category = Category::where('slug', $product->category)->first();

        Post::upsert([
            'source' => $product->source,
            'title' => $product->title,
            'description' => $product->description,
            'category_id' => $category->id,
            'price' => (int) $product->price,
            'user_id' => $userId,
            'is_rebate' => $product->is_rebate,
            'is_trade' => $product->is_trade,
            'address' => $product->address_string,
            'moderation_id' => RefModeration::IS_NOT_APPROVED,
            'published_at' => null,
            'ref_city_id' => $product->ref_city_id,
        ], ['source']);

        $post = Post::withoutGlobalScopes()->where('source', $product->source)->firstOrFail();
        $post->slug = $post->id.'_'.Str::slug($post->title);

        $this->setPostAttributes($post, $product->attributes);
        $post->save();

        return $post;
    }

    /**
     * @throws ConnectionException
     */
    public function parsePost(string $url, int $userId): array
    {
        $indexModel = IndexGunsBrokerPost::where('source', $url)->first();
        if ($indexModel) {
            return IndexGunsBrokerPostResource::make($indexModel)->jsonSerialize();
        }

        $body = $this->parseLink($url);
        $dom = HtmlDomParser::str_get_html($body);

        // Извлекаем `data-id` и `data-check` для номера телефона
        $data_id = $dom->find('.page-product__price-btn', 0)?->getAttribute('data-id');
        $data_check = $dom->find('.page-product__price-btn', 0)?->getAttribute('data-check');

        $user_name = $dom->find('.page-product__desc--seller strong a', 0)?->plaintext;
        $user_avatar = $dom->find('.page-product__desc--seller a img', 0)?->src;
        $user_id = $dom->find('.page-product__desc--seller strong a', 0)?->href;
        $user_id = (int) preg_replace('/\D/', '', $user_id);
        $user_phone = $this->getPhone($data_id, $data_check);

        $title = $dom->find('main.main__content h1', 0)?->plaintext;
        $title = $this->removeExtraSpaces($title);

        $location = $dom->find('.page-product__header--location', 0)?->plaintext;
        $location = $this->removeExtraSpaces(trim($location));

        $price = $dom->find('.price_h2', 0)?->plaintext;
        $price = (int) preg_replace('/\D/', '', $price);

        $description = $dom->find('.page-product__content--desc p', 0)?->plaintext;

        $is_rebate = (bool) $dom->find('.page-product__price.rebate', 0);
        $is_trade = (bool) $dom->find('.page-product__price.trade', 0);

        $category = $dom->find('.bread-crumbs__item', 1)?->href;
        $category = preg_replace('/[^A-Za-z0-9.!?]/', '', $category);

        $parametrs = $dom->find('.page-product__desc--table div');

        $structuredData = [];
        $lastKey = null;

        foreach ($parametrs as $parametr) {
            $text = trim($parametr?->plaintext);

            // Если текст оканчивается на ":", считаем его ключом
            if (Str::endsWith($text, ':')) {
                $lastKey = rtrim($text, ':');
            } elseif ($lastKey) {
                // Если есть сохранённый ключ, то это значение для него
                $structuredData[$lastKey] = $text;
                $lastKey = null; // Сбрасываем ключ
            }
        }

        $attributes = $this->setAttributes($structuredData);

        $images = $dom->find('#product-slider.owl-carousel img');
        $photos = [];
        foreach ($images as $img) {
            $photos[] = [
                'url' => $img->src,
                'filename' => md5($img->src),
            ];
        }

        $address_id = null;

        try {
            $address_id = $this->getAddress($location);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
        }

        if (in_array($user_avatar, ['/img/u/nopic.jpg', 'https://gunsbroker.ru/img/u/nopic.jpg'])) {
            $user_avatar = null;
        }

        $postData = [
            'category' => $category,
            'title' => $title,
            'address' => $location,
            'address_id' => $address_id,
            'price' => $price,
            'is_rebate' => $is_rebate,
            'is_trade' => $is_trade,
            'description' => $description,
            'created_at' => now(),
            'updated_at' => now(),
            'user_avatar' => $user_avatar,
            'user_id' => $user_id,
            'user_name' => $user_name,
            'user_phone' => $user_phone,
            'attributes' => $attributes,
            'source_attributes' => $structuredData,
            'photos' => $photos,
            'source' => $url,
            'db_user_id' => $userId,
        ];

        if ($address_id) {
            $address = IndexAddress::find($address_id);
            if ($address) {
                $postData['address_string'] = $address->result;
                $region_code = $address->region_iso_code;
                $city = RefCity::where('region_iso_code', $region_code)->first();

                if ($city?->id) {
                    $postData['ref_city_id'] = $city->id;
                }
            } else {
                Log::error("Address not found for $url address_id: $address_id ");
            }
        } else {
            Log::error('Address not found for '.$url);
        }

        IndexGunsBrokerPost::withoutRefresh()->updateOrCreate(
            ['source' => $url],
            $postData
        );

        return $postData;
    }

    private function setPostAttributes(Post $post, array $parameters): void
    {
        foreach ($parameters as $parameter) {
            PostAttribute::firstOrCreate([
                'post_id' => $post->id,
                'attributeable_type' => $parameter['attributeable_type'],
                'attributeable_id' => $parameter['attributeable_id'],
            ]);
        }
    }

    /**
     * @throws \Throwable
     */
    public function removeWatermark(Post $post, string $photo): ?Media
    {
        $medias = $post->getMedia('images');
        $filename = md5($photo);
        $has = $medias->where('name', $filename)->first();

        if ($has?->path && str_contains($has->path, 'gunsbroker.ru') === false) {
            return $has;
        }

        $response = retry(10, function () use ($photo) {
            return Http::post('http://watermark-api:3001/remove', [
                'url' => $photo,
            ]);
        }, 10000);

        $tempPath = tempnam(sys_get_temp_dir(), 'images');
        file_put_contents($tempPath, $response->body());

        $media = $post->addMedia(
            file: $tempPath,
            collectionName: 'images',
            name: $filename
        );

        unlink($tempPath);

        return $media;
    }

    /**
     * @throws ConnectionException
     */
    private function parseLink($url): string
    {
        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
            'cookies' => $this->cookieJar,
        ])->get($url);

        return $response->body();
    }

    private function getPhone($data_id, $data_check): string
    {
        $url = "https://gunsbroker.ru/phpjs/get_number.php?n={$data_id}&c={$data_check}";

        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
            'cookies' => $this->cookieJar,
        ])->get($url);

        return (string) new PhoneNumber($response->body(), 'RU');
    }

    private function getAddress($string)
    {
        $address = IndexAddress::where('source', $string)->first();

        if ($address?->_id) {
            return $address->_id;
        }

        $response = Http::withOptions([
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Token '.config('services.dadata.api_key'),
                'X-Secret' => config('services.dadata.api_secret'),
            ],
        ])->post('https://cleaner.dadata.ru/api/v1/clean/address', [
            $string,
        ]);

        $json = $response->json();

        $address_id = null;
        foreach ($json as $index => $data) {
            $address = new IndexAddress;
            $address->fill($data);
            $address->save();

            if ($index === 0) {
                $address_id = $address->_id;
            }
        }

        return $address_id ?? $address->_id;
    }

    private function removeExtraSpaces($string): array|string|null
    {
        return preg_replace('/\s+/', ' ', trim($string));
    }

    private function parseUserProfile($url): array
    {
        $response = Http::withOptions([
            'proxy' => config('services.proxy.url'),
            'verify' => false,
            'cookies' => $this->cookieJar,
        ])->get($url);

        $dom = HtmlDomParser::str_get_html($response->body());

        $user_name = $dom->find('.user-profile__name', 0)?->plaintext;
        $user_name = $this->removeExtraSpaces($user_name);

        $user_avatar = $dom->find('.user-profile__avatar img', 0)?->src;
        $user_id = (int) preg_replace('/\D/', '', $url);

        // Получаем телефон из профиля
        $phone_element = $dom->find('.user-profile__phone', 0);
        $data_id = $phone_element?->getAttribute('data-id');
        $data_check = $phone_element?->getAttribute('data-check');
        $phone = $this->getPhone($data_id, $data_check);

        $location = $dom->find('.user-profile__location', 0)?->plaintext;
        $location = $this->removeExtraSpaces(trim($location));

        $address_id = null;
        try {
            $address_id = $this->getAddress($location);
        } catch (\Throwable $throwable) {
            // Игнорируем ошибку при получении адреса
        }

        if (in_array($user_avatar, ['/img/u/nopic.jpg', 'https://gunsbroker.ru/img/u/nopic.jpg'])) {
            $user_avatar = null;
        }

        // Получаем список объявлений пользователя
        $posts = [];
        $post_elements = $dom->find('.main__item');

        foreach ($post_elements as $post_element) {
            $post_link = $post_element->find('a', 0)?->href;
            if ($post_link) {
                $post_link = 'https://gunsbroker.ru'.$post_link;
                $posts[] = $post_link;
            }
        }

        return [
            'name' => $user_name,
            'avatar' => $user_avatar,
            'phone' => $phone,
            'address' => $location,
            'address_id' => $address_id,
            'posts' => $posts,
        ];
    }

    private function setAttributes(array $parameters): array
    {
        $data = [];

        foreach ($parameters as $parameter => $value) {
            if (Str::lower($parameter) === 'состояние') {
                $condition = RefCondition::whereRaw('LOWER(name) = ?', [Str::lower($value)])->first();
                if (! $condition) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefCondition::class,
                    'attributeable_id' => $condition->id,
                ];
            }

            if (Str::lower($parameter) === 'тип') {
                $type = RefGunType::whereRaw('LOWER(name) = ?', [Str::lower($value)])->first();
                if (! $type) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefGunType::class,
                    'attributeable_id' => $type->id,
                ];
            }

            if (Str::lower($parameter) === 'год выпуска') {
                $year = (int) $value;
            }

            if (Str::lower($parameter) === 'cal') {
                $caliber = RefCaliber::where('slug', $value)->first();
                if (! $caliber) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefCaliber::class,
                    'attributeable_id' => $caliber->id,
                ];

                $data[] = [
                    'attributeable_type' => RefGunType::class,
                    'attributeable_id' => $caliber->ref_gun_type_id,
                ];
            }

            if (Str::lower($parameter) === 'reloading-type') {
                $reloading = RefGunReloading::where('slug', $value)->first();
                if (! $reloading) {
                    continue;
                }

                $data[] = [
                    'attributeable_type' => RefGunReloading::class,
                    'attributeable_id' => $reloading->id,
                ];
            }
        }

        return $data;
    }
}
