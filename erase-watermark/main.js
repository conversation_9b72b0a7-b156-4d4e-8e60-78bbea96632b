import express from "express";
import DeWatermark from "./index.ts";
import { SocksProxyAgent } from 'socks-proxy-agent';
import { getProxyHttpAgent } from 'proxy-http-agent';

import axios from "axios";

const app = express();
const port = 3001;

app.use(express.json()); // Позволяет получать JSON в теле запроса

app.post("/remove", async (req, res) => {
    try {
        const { url } = req.body;

        if (!url) {
            return res.status(400).json({ message: "url обязателен" });
        }

        const agent = new SocksProxyAgent(`socks5h://tor-proxy:9050`, {
            rejectUnauthorized: false
        });
        const proxy = {
            protocol: 'https',
            host: 'brd.superproxy.io',
            port: 33335,
            auth: {
                username: 'brd-customer-hl_b755c228-zone-residential_proxy2',
                password: 'a985yxnee9to'
            }
        };

        const dewatermark = new DeWatermark(agent);

        // Скачиваем изображение по URL
        const response = await axios.get(url, { responseType: "arraybuffer" });
        const imageBuffer = Buffer.from(response.data);

        const result = await dewatermark.eraseWatermark(imageBuffer);

        res.set("Content-Type", "image/png");
        res.send(result);
    } catch (error) {
        console.error(error);
        res.status(error.code || 500).json({ message: error.message || "Unknown error" });
    }
});

app.listen(port, () => {
    console.log(`API запущен на порту ${port}`);
});