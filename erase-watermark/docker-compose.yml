version: '3'

services:
  # Сервис с прокси через Tor
  tor-proxy:
    image: "dperson/torproxy"
    container_name: tor-proxy
    environment:
      - TOR_MaxCircuitDirtiness=1
      - TOR_NewCircuitPeriod=1
      - TOR_EnforceDistinctSubnets=1
    ports:
      - "9050:9050"
    networks:
      - watermark-network
    restart: always

  # Ваш API-сервис
  watermark-api:
    build: .
    ports:
      - "3001:3001"
    volumes:
      - .:/app
    networks:
      - watermark-network
    depends_on:
      - tor-proxy
    restart: always

networks:
  watermark-network:
    driver: bridge