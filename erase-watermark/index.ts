import axios from "axios";
import { SignJWT } from "jose";

export default class DeWatermark {
    private agent?: any;

    constructor(agent?: any) {
        this.agent = agent;
    }

    public async eraseWatermark(image: Buffer) {
        const { data: ip } = await axios.get("https://icanhazip.com", {
            httpsAgent: this.agent,
        });

        const { data: html } = await axios.get("https://dewatermark.ai/upload", {
            httpsAgent: this.agent,
        });
        const { data: js } = await axios.get(`https://dewatermark.ai/_next/static/chunks/pages/_app${html.split("/_next/static/chunks/pages/_app")[1].split(".js")[0]}.js`, {
            httpsAgent: this.agent,
        });
        const jwtKey = js.split("https://api.dewatermark.ai\"")[1].split("\"")[1];

        const apiKey = "Bearer " + await this.createJWT(this.base64ToUint8Array(jwtKey), false);

        const payload = new FormData();
        payload.append("original_preview_image", new Blob([image]), "image.png");
        payload.append("zoom_factor", "2");

        const { data } = await axios.post("https://api.dewatermark.ai/api/object_removal/v5/erase_watermark", payload, {
            headers: {
                "X-Api-Mode": "AUTO",
                "X-Service": "REMOVE_WATERMARK",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": "https://dewatermark.ai/",
                "Origin": "https://dewatermark.ai",
                "Host": "api.dewatermark.ai",
                "Authorization": apiKey
            },
            httpsAgent: this.agent
        });

        if (!data.edited_image) throw new Error(data);

        return Buffer.from(data.edited_image.image, "base64");
    }

    private base64ToUint8Array(base64: string): Uint8Array {
        const binaryString = atob(base64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        return bytes;
    }

    private async createJWT(keyString: Uint8Array, er: boolean): Promise<string> {
        const key = await this.importKey(keyString);

        const jwt = await new SignJWT({
            sub: "ignore",
            platform: "web",
            is_pro: er,
            exp: Math.round(Date.now() / 1000) + 300
        })
            .setProtectedHeader({
                alg: "HS256",
                typ: "JWT"
            })
            .sign(key);

        return jwt;
    }

    private async importKey(en: Uint8Array): Promise<CryptoKey> {
        return await crypto.subtle.importKey(
            "raw",
            en,
            { name: "HMAC", hash: "SHA-256" },
            false,
            ["sign", "verify"]
        );
    }
}